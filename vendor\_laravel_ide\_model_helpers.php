<?php

namespace App\Models {

    /**
     * App\Models\Card
     *
     * @property int $maxCopie
     * @property string|null $backArt
     * @property string|null $frontArt
     * @property datetime:Y-m-d H:i $uscita
     * @property string $artista
     * @property string|null $arena
     * @property string $tratti
     * @property string $descrizione
     * @property integer|null $potenza
     * @property integer|null $vita
     * @property integer $costo
     * @property string $rarita
     * @property string $tipo
     * @property string $titolo
     * @property string $nome
     * @property boolean $unica
     * @property string|null $aspettoSecondario
     * @property string|null $aspettoPrimario
     * @property integer $numero
     * @property string $espansione
     * @property string $cid
     * @property-read mixed $id
     * @property-read mixed $snippet
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereCid($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereEspansione($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereNumero($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereAspettoprimario($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereAspettosecondario($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereUnica($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereNome($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereTitolo($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereTipo($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereRarita($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereCosto($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereVita($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card wherePotenza($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereDescrizione($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereTratti($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereArena($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereArtista($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereUscita($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereFrontart($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereBackart($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card whereMaxcopie($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Card>|Card query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Card extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Composition
     *
     * @property string $id
     * @property int $copie
     * @property bool $foil
     * @property int $idMazzo
     * @property int $numero
     * @property string $espansione
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition whereEspansione($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition whereNumero($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition whereIdmazzo($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition whereFoil($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition whereCopie($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Composition>|Composition query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Composition extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Deck
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property integer $versione
     * @property integer $codUtente
     * @property boolean $public
     * @property string $nome
     * @property integer $id
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck whereNome($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck wherePublic($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck whereCodutente($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck whereVersione($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Deck>|Deck query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Deck extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\SystemError
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $resolved_at
     * @property int|null $resolved_by
     * @property string|null $admin_notes
     * @property mixed $status
     * @property int|null $user_id
     * @property string|null $user_agent
     * @property string|null $request_method
     * @property string|null $request_url
     * @property string $trace
     * @property int $line
     * @property string $file
     * @property string $message
     * @property string $exception_class
     * @property int $id
     * @property-read mixed $status_badge_color
     * @property-read mixed $status_display
     * @property-read mixed $short_file
     * @property-read \App\Models\User $user
     * @property-read \App\Models\User $resolvedBy
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereExceptionClass($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereMessage($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereFile($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereLine($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereTrace($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereRequestUrl($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereRequestMethod($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereUserAgent($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereAdminNotes($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereResolvedBy($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereResolvedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError unresolved()
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError resolved()
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<SystemError>|SystemError query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class SystemError extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\User
     *
     * @property bool $admin
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property string|null $remember_token
     * @property string $password
     * @property \Illuminate\Support\Carbon|null $email_verified_at
     * @property string $email
     * @property string $name
     * @property int $id
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
     * @property-read int|null $notifications_count
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereEmailVerifiedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User wherePassword($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereRememberToken($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereAdmin($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class User extends \Illuminate\Foundation\Auth\User
    {
        //
    }

}